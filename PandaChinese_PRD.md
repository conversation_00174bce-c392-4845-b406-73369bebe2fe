# PandaChinese 产品需求文档 (PRD)

---

| 版本 | 日期       | 作者 | 备注     |
| :--- | :--------- | :--- | :------- |
| V1.4 | 2025-09-24 | Kilo Code | UI全面重新设计，优化用户体验，完善交互功能 |
| V1.3 | 2025-09-24 | Kilo Code | 更新导航栏结构，新增汉字学习和成语学习模块，UI重新设计 |
| V1.2 | 2025-09-24 | Kilo Code | 新增拼音中心，补充详细技术栈 |
| V1.1 | 2025-09-24 | Kilo Code | MVP版本重构，聚焦听说读 |
| V1.0 | 2025-09-24 | Kilo Code | 初稿     |

## 1. 项目概述

### 1.1. 项目背景

`PandaChinese` 是一个面向母语为英语的中文学习者的在线教育平台。本项目旨在利用前沿的AI技术，打造一个以**AI对话**为核心，覆盖**拼音学习、汉字学习、成语学习、听读训练**全方位的沉浸式、个性化学习环境。

### 1.2. 产品目标 (MVP阶段)

*   **核心目标**：打造市场上最智能、最实用的AI中文学习平台，提供从基础拼音到高级成语的全方位学习体验。
*   **用户目标**：帮助用户从零基础开始，通过AI对话、拼音学习、汉字学习、成语学习等模块，系统性地掌握中文，建立开口说中文的自信。

### 1.3. 目标用户画像 (Persona)

*   **姓名**: Alex
*   **年龄**: 22-35岁
*   **职业**: 大学生、年轻职场人士
*   **核心痛点**: 对中文感兴趣但不知如何入门；学习了基础知识但缺乏真实对话环境，导致"哑巴中文"。

## 2. 产品核心设计原则

1.  **对话优先 (Conversation-First)**：产品的核心路径是引导用户进行对话练习。
2.  **基础扎实 (Solid Foundation)**：提供系统的拼音、汉字、成语学习模块，确保用户掌握扎实基础。
3.  **AI驱动 (AI-Driven)**：核心学习环节由AI赋能，提供个性化和互动性。
4.  **即时反馈 (Instant Feedback)**：提供即时、可操作的反馈。
5.  **场景化学习 (Contextual Learning)**：将学习内容融入真实场景。
6.  **轻度激励 (Light Gamification)**：通过排行榜等简单机制，保持学习动力。

## 3. 功能需求 (MVP)

### 模块一：AI对话 (核心模块)

*   **3.1.1. 场景库**
    *   **需求**: 提供丰富的对话场景库，按生活、工作等主题分类，并划分初、中、高三级难度。
*   **3.1.2. 实时对话引擎**
    *   **需求**: 用户与AI进行角色扮演对话，支持语音和文字输入。
*   **3.1.3. 对话后分析报告**
    *   **需求**: 对话结束后，生成包含发音评估、语法建议、新词汇总结的分析报告。

### 模块二：拼音学习 (Pinyin Learning)

*   **3.2.1. 拼音表学习**
    *   **需求**: 提供完整的声母、韵母、整体认读音节表。用户可以点击任意拼音，听到标准发音，并看到对应的口型图示。
*   **3.2.2. 声调训练**
    *   **需求**: 针对中文的四个声调进行专项练习。提供单音节、双音节的声调组合跟读。
*   **3.2.3. AI发音评估**
    *   **需求**: 用户跟读拼音，AI实时分析其发音准确性，特别是声调，并提供可视化反馈。

### 模块三：汉字学习 (Character Learning)

*   **3.3.1. 汉字结构学习**
    *   **需求**: 提供汉字的笔画顺序、部首结构、字形演变等基础知识学习。
*   **3.3.2. 汉字书写练习**
    *   **需求**: 交互式汉字书写练习，支持笔画顺序指导和书写评估。
*   **3.3.3. 词汇扩展**
    *   **需求**: 基于核心汉字，扩展相关词汇和短语，提供语境化学习。

### 模块四：成语学习 (Idiom Learning)

*   **3.4.1. 成语故事**
    *   **需求**: 提供成语的历史故事、文化背景，帮助用户理解成语的深层含义。
*   **3.4.2. 成语应用**
    *   **需求**: 通过情景对话和例句，教授成语的正确使用方法。
*   **3.4.3. 成语游戏**
    *   **需求**: 设计互动游戏，如成语接龙、成语填空等，增强学习趣味性。

### 模块五：听读中心 (Listening & Reading)

*   **3.5.1. 核心词汇学习**
    *   **需求**: 提供与对话场景相关的核心词汇学习，包含AI发音教练和智能闪卡复习功能。
*   **3.5.2. 分级阅读材料**
    *   **需求**: 提供简短的分级文章或段落，用于阅读和听力练习。

### 模块六：排行榜 (Leaderboard)

*   **3.6.1. 学习排行榜**
    *   **需求**: 设立周排行榜，根据"有效学习时长"进行排名，包含AI对话、拼音练习、汉字学习等各模块的综合评分。
*   **3.6.2. 成就系统**
    *   **需求**: 设计学习徽章和成就，激励用户持续学习。

### 模块七：用户系统

*   **3.7.1. 用户中心**
    *   **需求**: 支持邮箱登录，并记录学习数据。
*   **3.7.2. 学习进度追踪**
    *   **需求**: 提供个性化学习报告和进度可视化。

## 4. UI设计规范

### 4.1. 设计风格
*   **配色方案**: 现代化蓝色系主色调配合浅灰色辅助色
*   **主色调**: #2563eb (蓝色-600)，#3b82f6 (蓝色-500)
*   **辅助色**: #f8fafc (浅灰-50)，#6b7280 (灰色-500)
*   **设计原则**: 简洁现代，无渐变文字，注重用户体验

### 4.2. 导航结构
*   **主导航**: AI对话、拼音、汉字、成语、听读、排行
*   **布局**: 固定顶部导航栏，磨砂玻璃效果，响应式设计
*   **交互**: Logo点击回到首页，统一的语言切换功能

### 4.3. 用户体验优化
*   **无缝滚动**: 用户评价部分实现无缝循环滚动
*   **渐变遮罩**: 滚动区域左右添加渐变遮罩，消除生硬边缘
*   **磨砂玻璃**: 导航栏采用backdrop-filter实现现代磨砂效果
*   **响应式**: 全面适配移动端和桌面端

## 5. 技术栈选型

*   **前端**:
    *   **框架**: `React` + `Vite` - 现代化的、高效的开发体验
    *   **样式**: `TailwindCSS` - 提供原子化的CSS类，现代化蓝色系UI设计
*   **后端 (BaaS - Backend as a Service)**:
    *   **平台**: `Supabase` - 提供强大的免费套餐，包含PostgreSQL数据库、用户认证、对象存储等
*   **AI服务**:
    *   **LLM**: `Groq` 或 `OpenAI API` - 对话核心引擎
    *   **STT**: 浏览器原生 `Web Speech API` - 语音识别
    *   **TTS**: 浏览器原生 `Web Speech API` 或 `ElevenLabs` - 语音合成

## 6. 用户反馈与社会证明

### 6.1. 用户评价系统
*   **评价展示**: 超过10,000+学习者给予5星好评
*   **滚动展示**: 10条真实用户评价，循环滚动显示
*   **国际化**: 评价者使用英文姓名，体现国际用户群体

### 6.2. 社交媒体整合
*   **平台覆盖**: Twitter、Facebook、Instagram、LinkedIn、GitHub、微信
*   **统一设计**: 所有社交媒体图标采用统一的视觉风格

## 7. 多语言支持

### 7.1. 语言切换
*   **支持语言**: 简体中文、English
*   **切换方式**: 导航栏和Footer均提供语言切换功能
*   **一致性**: 两处语言切换保持完全一致的交互体验

### 7.2. 本地化内容
*   **导航简化**: 中英文导航文字都进行了简化，保持视觉一致性
*   **动态年份**: 版权信息使用JavaScript动态获取当前年份

---

**更新日志 V1.4**:
- 完成UI全面重新设计，采用现代化蓝色系配色
- 实现用户评价无缝循环滚动效果
- 优化导航栏磨砂玻璃效果和交互体验
- 修复滚动条闪烁问题
- 添加Logo点击回首页功能
- 统一中英文语言切换体验
- 完善Footer区域内容展示
- 优化响应式设计和移动端适配
