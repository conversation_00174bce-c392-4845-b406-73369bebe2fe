@segmented-prefix-cls: rc-segmented;

@disabled-color: fade(#000, 25%);
@selected-bg-color: white;
@text-color: #262626;
@transition-duration: 0.3s;
@transition-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);

.segmented-disabled-item() {
  &,
  &:hover,
  &:focus {
    color: @disabled-color;
    cursor: not-allowed;
  }
}

.segmented-item-selected() {
  background-color: @selected-bg-color;
}

.@{segmented-prefix-cls} {
  display: inline-block;
  padding: 2px;
  background-color: rgba(0, 0, 0, 0.04);

  &-group {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: stretch;
    justify-content: flex-start;
    width: 100%;
    border-radius: 2px;
  }

  &-item {
    position: relative;
    min-height: 28px;
    padding: 4px 10px;
    color: fade(#000, 85%);
    text-align: center;
    cursor: pointer;

    &-selected {
      .segmented-item-selected();
      color: @text-color;
    }

    &:hover,
    &:focus {
      color: @text-color;
    }

    &-disabled {
      .segmented-disabled-item();
    }

    &-label {
      z-index: 2;
      line-height: 24px;
    }

    &-input {
      position: absolute;
      top: 0;
      left: 0;
      width: 0;
      height: 0;
      opacity: 0;
      pointer-events: none;
    }
  }

  &-thumb {
    .segmented-item-selected();
    position: absolute;
    width: 0;
    height: 100%;
    padding: 4px 0;
    transition: transform @transition-duration @transition-timing-function,
      width @transition-duration @transition-timing-function;
  }

  &-vertical &-group {
    flex-direction: column;
  }

  &-vertical &-item {
    width: 100%;
    text-align: left;
  }

  &-vertical &-thumb {
    width: 100%;
    height: 0;
    padding: 0 4px;
    transition: transform @transition-duration @transition-timing-function,
      height @transition-duration @transition-timing-function;
  }

  // disabled styles
  &-disabled &-item,
  &-disabled &-item:hover,
  &-disabled &-item:focus {
    .segmented-disabled-item();
  }

  &-thumb-motion-appear-active,
  &-thumb-motion-enter-active {
    transition: transform @transition-duration @transition-timing-function,
      width @transition-duration @transition-timing-function;
    will-change: transform, width;
  }

  &-rtl {
    direction: rtl;
  }
}

.rc-segmented-item {
  &:focus {
    outline: none;
  }

  &-focused {
    border-radius: 2px;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}
