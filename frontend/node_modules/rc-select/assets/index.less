@select-prefix: ~'rc-select';

* {
  box-sizing: border-box;
}

.search-input-without-border() {
  .@{select-prefix}-selection-search-input {
    border: none;
    outline: none;
    background: rgba(255, 0, 0, 0.2);
    width: 100%;
  }
}

.@{select-prefix} {
  display: inline-block;
  font-size: 12px;
  width: 100px;
  position: relative;

  &-disabled {
    &,
    & input {
      cursor: not-allowed;
    }

    .@{select-prefix}-selector {
      opacity: 0.3;
    }
  }

  &-show-arrow&-loading {
    .@{select-prefix}-arrow {
      &-icon::after {
        box-sizing: border-box;
        width: 12px;
        height: 12px;
        border-radius: 100%;
        border: 2px solid #999;
        border-top-color: transparent;
        border-bottom-color: transparent;
        transform: none;
        margin-top: 4px;

        animation: rcSelectLoadingIcon 0.5s infinite;
      }
    }
  }

  // ============== Selector ===============
  .@{select-prefix}-selection-placeholder {
    opacity: 0.4;
    pointer-events: none;
  }

  // ============== Search ===============
  .@{select-prefix}-selection-search-input {
    appearance: none;

    &::-webkit-search-cancel-button {
      display: none;
      appearance: none;
    }
  }

  // --------------- Single ----------------
  &-single {
    .@{select-prefix}-selector {
      display: flex;
      position: relative;

      .@{select-prefix}-selection-wrap {
        width: 100%;
        position: relative;
      }

      .@{select-prefix}-selection-search {
        width: 100%;
        position: relative;

        &-input {
          width: 100%;
        }
      }

      .@{select-prefix}-selection-item,
      .@{select-prefix}-selection-placeholder {
        position: absolute;
        top: 1px;
        left: 3px;
        pointer-events: none;
        font-weight: normal;
      }
    }

    // Not customize
    &:not(.@{select-prefix}-customize-input) {
      .@{select-prefix}-selector {
        padding: 1px;
        border: 1px solid #000;

        .search-input-without-border();
      }
    }
  }

  // -------------- Multiple ---------------
  &-multiple .@{select-prefix}-selector {
    display: flex;
    padding: 1px;
    border: 1px solid #000;

    .@{select-prefix}-selection-item {
      flex: none;
      background: #bbb;
      border-radius: 4px;
      margin-right: 2px;
      padding: 0 8px;

      &-disabled {
        cursor: not-allowed;
        opacity: 0.5;
      }
    }

    .@{select-prefix}-selection-overflow {
      display: flex;
      flex-wrap: wrap;

      &-item {
        flex: none;
        max-width: 100%;
      }
    }

    .@{select-prefix}-selection-search {
      position: relative;
      max-width: 100%;

      &-input,
      &-mirror {
        padding: 1px;
        font-family: system-ui;
      }

      &-mirror {
        position: absolute;
        z-index: 999;
        white-space: nowrap;
        position: none;
        left: 0;
        top: 0;
        visibility: hidden;
      }
    }

    .search-input-without-border();
  }

  // ================ Icons ================
  &-allow-clear {
    &.@{select-prefix}-multiple .@{select-prefix}-selector {
      padding-right: 20px;
    }

    .@{select-prefix}-clear {
      position: absolute;
      right: 20px;
      top: 0;
    }
  }

  &-show-arrow {
    &.@{select-prefix}-multiple .@{select-prefix}-selector {
      padding-right: 20px;
    }

    .@{select-prefix}-arrow {
      pointer-events: none;
      position: absolute;
      right: 5px;
      top: 0;

      &-icon::after {
        content: '';
        border: 5px solid transparent;
        width: 0;
        height: 0;
        display: inline-block;
        border-top-color: #999;
        transform: translateY(5px);
      }
    }
  }

  // =============== Focused ===============
  &-focused {
    .@{select-prefix}-selector {
      border-color: blue !important;
    }
  }

  // ============== Dropdown ===============
  &-dropdown {
    border: 1px solid green;
    min-height: 100px;
    position: absolute;
    background: #fff;

    &-hidden {
      display: none;
    }
  }

  // =============== Option ================
  &-item {
    font-size: 16px;
    line-height: 1.5;
    padding: 4px 16px;

    // >>> Group
    &-group {
      color: #999;
      font-weight: bold;
      font-size: 80%;
    }

    // >>> Option
    &-option {
      position: relative;

      &-grouped {
        padding-left: 24px;
      }

      .@{select-prefix}-item-option-state {
        position: absolute;
        right: 0;
        top: 4px;
        pointer-events: none;
      }

      // ------- Active -------
      &-active {
        background: #ddd;
      }

      // ------ Disabled ------
      &-disabled {
        color: #999;
      }
    }

    // >>> Empty
    &-empty {
      text-align: center;
      color: #999;
    }
  }
}

.@{select-prefix}-selection__choice-zoom {
  transition: all 0.3s;
}

.@{select-prefix}-selection__choice-zoom-appear {
  opacity: 0;
  transform: scale(0.5);

  &&-active {
    opacity: 1;
    transform: scale(1);
  }
}
.@{select-prefix}-selection__choice-zoom-leave {
  opacity: 1;
  transform: scale(1);

  &&-active {
    opacity: 0;
    transform: scale(0.5);
  }
}

.effect() {
  animation-duration: 0.3s;
  animation-fill-mode: both;
  transform-origin: 0 0;
}

.@{select-prefix}-dropdown {
  &-slide-up-enter,
  &-slide-up-appear {
    .effect();
    opacity: 0;
    animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
    animation-play-state: paused;
  }

  &-slide-up-leave {
    .effect();
    opacity: 1;
    animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.34);
    animation-play-state: paused;
  }

  &-slide-up-enter&-slide-up-enter-active&-placement-bottomLeft,
  &-slide-up-appear&-slide-up-appear-active&-placement-bottomLeft,
  &-slide-up-enter&-slide-up-enter-active&-placement-bottomRight,
  &-slide-up-appear&-slide-up-appear-active&-placement-bottomRight {
    animation-name: rcSelectDropdownSlideUpIn;
    animation-play-state: running;
  }

  &-slide-up-leave&-slide-up-leave-active&-placement-bottomLeft,
  &-slide-up-leave&-slide-up-leave-active&-placement-bottomRight {
    animation-name: rcSelectDropdownSlideUpOut;
    animation-play-state: running;
  }

  &-slide-up-enter&-slide-up-enter-active&-placement-topLeft,
  &-slide-up-appear&-slide-up-appear-active&-placement-topLeft,
  &-slide-up-enter&-slide-up-enter-active&-placement-topRight,
  &-slide-up-appear&-slide-up-appear-active&-placement-topRight {
    animation-name: rcSelectDropdownSlideDownIn;
    animation-play-state: running;
  }

  &-slide-up-leave&-slide-up-leave-active&-placement-topLeft,
  &-slide-up-leave&-slide-up-leave-active&-placement-topRight {
    animation-name: rcSelectDropdownSlideDownOut;
    animation-play-state: running;
  }
}

@keyframes rcSelectDropdownSlideUpIn {
  0% {
    opacity: 0;
    transform-origin: 0% 0%;
    transform: scaleY(0);
  }
  100% {
    opacity: 1;
    transform-origin: 0% 0%;
    transform: scaleY(1);
  }
}
@keyframes rcSelectDropdownSlideUpOut {
  0% {
    opacity: 1;
    transform-origin: 0% 0%;
    transform: scaleY(1);
  }
  100% {
    opacity: 0;
    transform-origin: 0% 0%;
    transform: scaleY(0);
  }
}

@keyframes rcSelectDropdownSlideDownIn {
  0% {
    transform: scaleY(0);
    transform-origin: 100% 100%;
    opacity: 0;
  }

  100% {
    transform: scaleY(1);
    transform-origin: 100% 100%;
    opacity: 1;
  }
}

@keyframes rcSelectDropdownSlideDownOut {
  0% {
    transform: scaleY(1);
    transform-origin: 100% 100%;
    opacity: 1;
  }

  100% {
    transform: scaleY(0);
    transform-origin: 100% 100%;
    opacity: 0;
  }
}

@keyframes rcSelectLoadingIcon {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
