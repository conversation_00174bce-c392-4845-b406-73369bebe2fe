@prefix-cls: rc-picker;

@background-color: rgb(255, 240, 255);

@input-placeholder-color: hsv(0, 0, 75%);

@time-panel-padding-total: 180px;
// @time-panel-padding-top: 90px;
@time-panel-padding-top: 0;

.placeholder(@color: @input-placeholder-color) {
  // Firefox
  &::-moz-placeholder {
    opacity: 1; // Override Firefox's unusual default opacity; see https://github.com/twbs/bootstrap/pull/11526
  }

  &::placeholder {
    color: @color;
  }

  &:placeholder-shown {
    text-overflow: ellipsis;
  }
}

.@{prefix-cls} {
  position: relative;
  display: inline-flex;

  &-rtl {
    direction: rtl;
  }

  &-focused {
    border: 1px solid blue;
  }
  &-invalid {
    box-shadow: 0 0 2px red;
  }

  &-panels {
    display: flex;
    flex-wrap: nowrap;
  }

  &-panel {
    display: inline-block;
    vertical-align: top;
    background: @background-color;
    border: 1px solid #666;

    &-focused {
      border-color: blue;
    }

    &-rtl {
      direction: rtl;
    }
  }

  // ===================== Shared Panel =====================
  &-decade-panel,
  &-year-panel,
  &-month-panel,
  &-week-panel,
  &-date-panel,
  &-time-panel {
    display: flex;
    flex-direction: column;

    table {
      text-align: center;
      border-collapse: collapse;
    }
  }

  // Header
  &-header {
    display: flex;

    > * {
      flex: none;
    }

    &-view {
      flex: auto;
      text-align: center;

      > button {
        padding: 0;
        border: 0;
      }
    }
  }

  // Content
  &-cell {
    color: #aaa;

    &-disabled {
      opacity: 0.2;
    }

    &-inner {
      display: inline-block;
      box-sizing: border-box;
      width: 100%;
      height: 20px;
      margin: 0;
      padding: 0;
      font-size: 12px;
      line-height: 20px;
      background: transparent;
      border: 0;
      border: none;
      outline: none;
      cursor: pointer;
      transition:
        background 0.3s,
        border 0.3s;

      &:hover {
        background: fade(blue, 30%);
      }
    }

    &-in-view {
      color: #333;
    }

    &-in-range > &-inner {
      background: fade(blue, 5%);
    }

    &-hover > &-inner {
      background: orange;
    }

    &-range-hover-start,
    &-range-hover-end,
    &-range-hover {
      position: relative;
      &::after {
        position: absolute;
        top: 3px;
        right: 0;
        bottom: 0;
        left: 0;
        border: 1px solid green;
        border-right: 0;
        border-left: 0;
        content: '';
        pointer-events: none;
      }
    }

    &-range-hover-start::after {
      border-left: 1px solid green !important;
    }
    &-range-hover-end::after {
      border-right: 1px solid green !important;
    }

    &-today > &-inner {
      border: 1px solid blue;
    }
    &-range-start > &-inner,
    &-range-end > &-inner,
    &-selected > &-inner {
      background: fade(blue, 20%);
    }
  }

  // Preset
  &-presets {
    background: #ccccff;

    ul {
      margin: 0;
      padding: 0;
      list-style: none;
    }
  }

  &-footer,
  &-picker-footer {
    background: green;
  }

  &-ranges {
    margin: 0;
    padding: 0;
    overflow: hidden;
    list-style: none;

    > li {
      display: inline-block;
    }
  }

  &-ok {
    float: right;
  }

  // ================== Year & Month Panel ==================
  &-year-panel,
  &-month-panel {
    .@{prefix-cls}-cell-inner {
      width: 80px;
    }
  }

  // ====================== Week Panel ======================
  &-week-panel {
    &-row {
      &:hover {
        .@{prefix-cls}-cell {
          background: red;
        }
      }

      &-selected {
        .@{prefix-cls}-cell {
          background: rgba(0, 0, 255, 0.3);
        }
      }

      &-range {
        &-hover {
          .@{prefix-cls}-cell {
            background: rgba(0, 255, 0, 0.1);
          }
        }

        &-start,
        &-end {
          .@{prefix-cls}-cell {
            background: rgba(0, 255, 0, 0.3);
          }
        }
      }
    }

    .@{prefix-cls}-cell,
    .@{prefix-cls}-cell-inner {
      width: 20px;
    }

    .@{prefix-cls}-cell-week {
      color: #999;
      font-weight: bold;
      font-size: 12px;
    }

    .@{prefix-cls}-cell:hover > .@{prefix-cls}-cell-inner,
    .@{prefix-cls}-cell-selected > .@{prefix-cls}-cell-inner {
      background: transparent;
    }
  }

  // ====================== Date Panel ======================
  &-date-panel {
    .@{prefix-cls}-cell-inner {
      width: 20px;
    }
  }

  // ====================== Time Panel ======================
  &-time-panel {
    width: auto;

    .@{prefix-cls}-content {
      position: relative;
      display: flex;
      max-height: 200px;
      direction: ltr;

      // &::after {
      //   position: absolute;
      //   top: @time-panel-padding-top;
      //   right: -5px;
      //   left: -5px;
      //   height: 20px;
      //   background: rgba(255, 0, 0, 0.05);
      //   content: '';
      //   pointer-events: none;
      // }
    }

    // &-column-holder {
    //   display: flex;
    //   flex-direction: column;
    //   text-align: center;
    // }

    &-column-title {
      font-size: 14px;
      line-height: 20px;
    }

    &-column {
      flex: auto;
      width: 50px;
      margin: 0;
      // padding: 0 0 180px 0;
      padding: @time-panel-padding-top 0 (@time-panel-padding-total - @time-panel-padding-top);
      overflow-x: hidden;
      overflow-y: hidden;
      font-size: 12px;
      text-align: left;
      list-style: none;
      transition: background 0.3s;

      &-active {
        background: rgba(0, 0, 255, 0.1);
      }

      &:hover {
        overflow-y: auto;
      }

      > li {
        width: 50px;
        margin: 0;
        padding: 0;
        cursor: pointer;

        &.@{prefix-cls}-time-panel-cell {
          &-disabled {
            opacity: 0.5;
          }

          &-selected {
            background: rgba(0, 0, 255, 0.5);
          }
        }

        .@{prefix-cls}-time-panel-cell-inner {
          display: block;
          width: 100%;
          height: 20px;
          margin: 0;
          // padding: 0 0 0 12px;
          color: #333;
          line-height: 20px;
          text-align: center;

          .@{prefix-cls}-panel-rtl & {
            padding: 0 12px 0 0;
            text-align: right;
          }
        }
      }
    }
  }

  // ====================== Date Time =======================
  &-datetime-panel {
    display: flex;

    .@{prefix-cls}-time-panel {
      border-left: 1px solid #999;
    }

    .@{prefix-cls}-date-panel,
    .@{prefix-cls}-time-panel {
      transition: opacity 0.3s;
    }

    // Keyboard
    &-active {
      .@{prefix-cls}-date-panel,
      .@{prefix-cls}-time-panel {
        opacity: 0.3;

        &-active {
          opacity: 1;
        }
      }
    }
  }

  // ======================== Input =========================
  &-input {
    position: relative;
    display: inline-flex;
    width: 100%;

    .@{prefix-cls}-rtl & {
      text-align: right;
    }

    &-active {
      > input {
        background: rgba(0, 0, 255, 0.05);
      }
    }

    > input {
      width: 100%;
      .placeholder();
    }

    &-placeholder {
      > input {
        color: @input-placeholder-color;
      }
    }
  }

  &-clear {
    position: absolute;
    top: 0;
    inset-inline-end: 4px;
    cursor: pointer;

    &-btn::after {
      content: '×';
    }
  }

  // ======================= Dropdown =======================
  &-dropdown {
    position: absolute;
    box-shadow: 0 0 1px red;
    pointer-events: none;

    &-range {
      padding: 10px 0;
    }

    &-hidden {
      display: none;
    }

    &-rtl {
      direction: rtl;
    }

    // Panel
    @arrow-size: 10px;

    &-placement-topLeft,
    &-placement-topRight {
      .@{prefix-cls}-range-arrow {
        bottom: (@arrow-size / 2 + 1px);
        transform: rotate(135deg);
      }
    }
    &-placement-bottomLeft,
    &-placement-bottomRight {
      .@{prefix-cls}-range-arrow {
        top: (@arrow-size / 2 + 1px);
        transform: rotate(-45deg);
      }
    }

    .@{prefix-cls}-range-arrow {
      position: absolute;
      z-index: 1;
      width: @arrow-size;
      height: @arrow-size;
      transition: all 0.3s;

      &::before,
      &::after {
        position: absolute;
        top: 50%;
        inset-inline-start: 50%;
        box-sizing: border-box;
        transform: translate(-50%, -50%);
        content: '';

        .@{prefix-cls}-dropdown-rtl& {
          transform: translate(50%, -50%);
        }
      }

      &::before {
        width: @arrow-size;
        height: @arrow-size;
        border: (@arrow-size / 2) solid blue;
        border-color: blue blue transparent transparent;
      }
      &::after {
        width: @arrow-size - 2px;
        height: @arrow-size - 2px;
        border: ((@arrow-size - 2px) / 2) solid blue;
        border-color: @background-color @background-color transparent transparent;
      }
    }
  }

  // ========================================================
  // =                     Range Picker                     =
  // ========================================================
  &-range {
    position: relative;
    display: inline-flex;

    &-wrapper {
      display: flex;
    }

    .@{prefix-cls}-active-bar {
      bottom: 0;
      height: 3px;
      background: green;
      opacity: 0;
      transition: all 0.3s;
      pointer-events: none;
    }

    &.@{prefix-cls}-focused {
      .@{prefix-cls}-active-bar {
        opacity: 1;
      }
    }
  }

  &-panel-container {
    display: inline-block;
    vertical-align: top;
    transition: margin 0.3s;
    pointer-events: all;
  }

  &-panel-layout {
    display: flex;
    flex-wrap: nowrap;
    align-items: stretch;
  }

  // ========================================================
  // =                       Overflow                       =
  // ========================================================
  &-selector {
    width: 100%;
  }

  &-selection-overflow {
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
    width: 100%;
    border: 1px solid green;
    min-height: 1em;

    &-item {
      flex: none;
      max-width: 100%;
    }
  }

  &-selection-item {
    border: 1px solid blue;
  }

  &-selection-placeholder {
    pointer-events: none;
    position: absolute;
    left: 0;
    top: 0;
  }

  &-multiple-input {
    width: 10px;
    opacity: 0.1;
  }
}
