{"name": "pandachinese-frontend", "version": "1.0.0", "description": "PandaChinese前端应用", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "typecheck": "tsc --noEmit"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@ant-design/icons": "^6.0.2", "@supabase/supabase-js": "^2.57.4", "@tailwindcss/postcss": "^4.1.13", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.3", "antd": "^5.27.4", "autoprefixer": "^10.4.21", "i18next": "^25.5.2", "i18next-browser-languagedetector": "^8.2.0", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-i18next": "^15.7.3", "tailwindcss": "^4.1.13", "typescript": "^5.9.2", "vite": "^7.1.7"}, "devDependencies": {"@types/node": "^24.5.2", "@typescript-eslint/eslint-plugin": "^8.44.1", "@typescript-eslint/parser": "^8.44.1", "eslint": "^9.36.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0"}}