# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
build/
*.local

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vite specific
.vite/
vite.config.js.timestamp-*
vite.config.ts.timestamp-*

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE and editor files
.vscode/
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.idea/
*.swp
*.swo
*~
*.sublime-project
*.sublime-workspace

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Stylelint cache
.stylelintcache

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Storybook build outputs
.out
.storybook-out
storybook-static/

# Temporary folders
tmp/
temp/

# Rollup cache
.rollup.cache/

# TailwindCSS
.tailwindcss-cache

# Turbo
.turbo/

# Vercel
.vercel

# Webpack
.webpack/

# SvelteKit
.svelte-kit

# Local Netlify folder
.netlify

# Sentry Config File
.sentryclirc

# Wrangler
.wrangler/

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Husky
.husky/_

# Auto-generated files
*.generated.*

# Test results
test-results/
playwright-report/
playwright/.cache/

# Cypress
cypress/videos/
cypress/screenshots/
cypress/downloads/

# Jest
jest.config.js.timestamp-*

# React specific
.react/

# Next.js specific (if used in future)
.next/
out/

# Nuxt.js specific (if used in future)
.nuxt/
.output/

# Gatsby specific (if used in future)
.cache/
public/

# Bundle analyzer
bundle-analyzer-report.html

# Local development
.local

# Hot reload
.hot-update.*

# Source maps (optional, uncomment if you don't want to track them)
# *.map

# Lock files (uncomment if you want to ignore specific lock files)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Backup files
*.backup
*.bak
*.tmp

# Database
*.db
*.sqlite
*.sqlite3

# Supabase local development
.supabase/

# AI/ML models (if any)
*.model
*.weights
*.h5
*.pkl
*.joblib

# Audio/Video files (large files that shouldn't be in git)
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm
*.mp3
*.wav
*.flac
*.aac
*.ogg
*.wma

# Large image files (uncomment if needed)
# *.psd
# *.ai
# *.sketch

# Documentation build
docs/build/
docs/.docusaurus/

# Chromatic
build-storybook.log

# Sentry
.sentryclirc

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log
ui-debug.log

# Capacitor
android/
ios/
