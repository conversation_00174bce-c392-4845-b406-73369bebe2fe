// User types
export interface User {
  id: string;
  email: string;
  display_name?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

// Pinyin types
export interface Pinyin {
  id: string;
  pinyin: string;
  chinese: string;
  english: string;
  type: 'shengmu' | 'yunmu' | 'zhengti';
  tone?: number;
  audio_url?: string;
  image_url?: string;
}

// Conversation types
export interface Conversation {
  id: string;
  user_id: string;
  title: string;
  scenario: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: number;
  created_at: string;
  messages: Message[];
}

export interface Message {
  id: string;
  conversation_id: string;
  role: 'user' | 'assistant';
  content: string;
  audio_url?: string;
  created_at: string;
}

// Reading types
export interface ReadingMaterial {
  id: string;
  title: string;
  content: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  audio_url?: string;
  created_at: string;
}

// Vocabulary types
export interface Vocabulary {
  id: string;
  chinese: string;
  pinyin: string;
  english: string;
  category: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  audio_url?: string;
  image_url?: string;
}

// Progress types
export interface LearningProgress {
  user_id: string;
  pinyin_progress: number;
  conversation_progress: number;
  reading_progress: number;
  total_minutes: number;
  weekly_ranking?: number;
  last_active: string;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

// Component props types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface LoadingState {
  loading: boolean;
  error?: string;
}