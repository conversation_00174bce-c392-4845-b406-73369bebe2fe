import React from 'react'
import { <PERSON><PERSON>, But<PERSON> } from 'antd'
import { ReloadOutlined } from '@ant-design/icons'
import { BaseComponentProps } from '../types'

interface ErrorProps extends BaseComponentProps {
  message: string
  description?: string
  showRetry?: boolean
  onRetry?: () => void
}

const Error: React.FC<ErrorProps> = ({
  message,
  description = '请稍后重试',
  showRetry = true,
  onRetry,
  className = '',
}) => {
  return (
    <div className={className}>
      <Alert
        message={message}
        description={
          <div>
            <p>{description}</p>
            {showRetry && onRetry && (
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={onRetry}
                style={{ marginTop: 16 }}
              >
                重试
              </Button>
            )}
          </div>
        }
        type="error"
        showIcon
        action={
          showRetry && onRetry ? (
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={onRetry}
            >
              重试
            </Button>
          ) : undefined
        }
      />
    </div>
  )
}

export default Error