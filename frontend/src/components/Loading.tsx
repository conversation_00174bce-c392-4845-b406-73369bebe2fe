import React from 'react'
import { Spin } from 'antd'
import { BaseComponentProps } from '../types'

interface LoadingProps extends BaseComponentProps {
  size?: 'small' | 'default' | 'large'
  tip?: string
  fullscreen?: boolean
}

const Loading: React.FC<LoadingProps> = ({
  size = 'default',
  tip = '加载中...',
  fullscreen = false,
  className = '',
}) => {
  const loadingComponent = (
    <div className={`flex items-center justify-center ${className}`}>
      <Spin size={size} tip={tip} />
    </div>
  )

  if (fullscreen) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-80 flex items-center justify-center z-50">
        {loadingComponent}
      </div>
    )
  }

  return loadingComponent
}

export default Loading