import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// 导入语言资源
import zhTranslations from './locales/zh/index';
import enTranslations from './locales/en/index';

const resources = {
  zh: zhTranslations,
  en: enTranslations,
};

i18n
  // 检测用户语言
  .use(LanguageDetector)
  // 传递 i18n 实例给 react-i18next
  .use(initReactI18next)
  // 初始化 i18next
  .init({
    resources,
    
    // 语言检测选项
    detection: {
      // 检测顺序
      order: ['localStorage', 'navigator', 'htmlTag'],
      // 缓存用户语言
      caches: ['localStorage'],
      // localStorage 键名
      lookupLocalStorage: 'i18nextLng',
    },

    // 回退语言
    fallbackLng: 'zh',
    
    // 调试模式（生产环境应设为 false）
    debug: process.env.NODE_ENV === 'development',

    // 插值选项
    interpolation: {
      escapeValue: false, // React 已经默认转义
    },

    // 命名空间
    defaultNS: 'common',
    ns: ['common', 'navigation', 'pages'],

    // 键分隔符
    keySeparator: '.',
    
    // 命名空间分隔符
    nsSeparator: ':',
  });

export default i18n;
