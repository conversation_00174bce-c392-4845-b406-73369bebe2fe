import { createClient } from '@supabase/supabase-js'

const supabaseUrl = (import.meta as any).env.VITE_SUPABASE_URL
const supabaseAnonKey = (import.meta as any).env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.warn('Supabase配置未找到，请检查环境变量 VITE_SUPABASE_URL 和 VITE_SUPABASE_ANON_KEY')
}

export const supabase = createClient(supabaseUrl || '', supabaseAnonKey || '')

// 认证相关服务
export const authService = {
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    return { data, error }
  },

  async signUp(email: string, password: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    })
    return { data, error }
  },

  async signOut() {
    const { error } = await supabase.auth.signOut()
    return { error }
  },

  async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser()
    return { user, error }
  },

  onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback)
  },
}

// 用户相关服务
export const userService = {
  async getUserProfile(userId: string) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single()
    return { data, error }
  },

  async updateUserProfile(userId: string, updates: any) {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()
    return { data, error }
  },
}

// 拼音相关服务
export const pinyinService = {
  async getPinyinList(type?: 'shengmu' | 'yunmu' | 'zhengti') {
    let query = supabase.from('pinyin').select('*')
    if (type) {
      query = query.eq('type', type)
    }
    const { data, error } = await query
    return { data, error }
  },

  async getPinyinById(id: string) {
    const { data, error } = await supabase
      .from('pinyin')
      .select('*')
      .eq('id', id)
      .single()
    return { data, error }
  },
}

// 对话相关服务
export const conversationService = {
  async getConversations(userId: string) {
    const { data, error } = await supabase
      .from('conversations')
      .select(`
        *,
        messages (*)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
    return { data, error }
  },

  async createConversation(conversation: any) {
    const { data, error } = await supabase
      .from('conversations')
      .insert([conversation])
      .select()
      .single()
    return { data, error }
  },

  async addMessage(message: any) {
    const { data, error } = await supabase
      .from('messages')
      .insert([message])
      .select()
      .single()
    return { data, error }
  },
}

// 阅读材料服务
export const readingService = {
  async getReadingMaterials(level?: string, category?: string) {
    let query = supabase.from('reading_materials').select('*')
    if (level) {
      query = query.eq('level', level)
    }
    if (category) {
      query = query.eq('category', category)
    }
    const { data, error } = await query
    return { data, error }
  },

  async getReadingMaterialById(id: string) {
    const { data, error } = await supabase
      .from('reading_materials')
      .select('*')
      .eq('id', id)
      .single()
    return { data, error }
  },
}

// 学习进度服务
export const progressService = {
  async getUserProgress(userId: string) {
    const { data, error } = await supabase
      .from('learning_progress')
      .select('*')
      .eq('user_id', userId)
      .single()
    return { data, error }
  },

  async updateUserProgress(userId: string, updates: any) {
    const { data, error } = await supabase
      .from('learning_progress')
      .upsert([{ user_id: userId, ...updates }])
      .select()
      .single()
    return { data, error }
  },
}