import { FC } from 'react'
import { <PERSON><PERSON>, <PERSON>, Col, Typography, Dropdown, MenuProps } from 'antd'
import { useTranslation } from 'react-i18next'
import {
  PlayCircleOutlined,
  RightOutlined,
  GlobalOutlined,
  UserOutlined,
  WechatOutlined,
  FacebookOutlined,
  InstagramOutlined,
  TikTokOutlined
} from '@ant-design/icons'

const { Title, Paragraph } = Typography

const App: FC = () => {
  const { t, i18n } = useTranslation(['common', 'navigation', 'pages'])
  
  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng)
  }

  const languageMenuItems: MenuProps['items'] = [
    {
      key: 'zh',
      label: '中文',
      onClick: () => changeLanguage('zh')
    },
    {
      key: 'en',
      label: 'English',
      onClick: () => changeLanguage('en')
    }
  ]

  const containerStyle = {
    maxWidth: '1400px',
    margin: '0 auto',
    padding: '0 24px'
  }

  const sectionStyle = {
    padding: '80px 0'
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#ffffff' }}>
      {/* Header */}
      <header style={{
        position: 'fixed',
        top: 0,
        width: '100%',
        backgroundColor: 'rgba(255, 255, 255, 0.85)',
        backdropFilter: 'blur(20px) saturate(180%)',
        WebkitBackdropFilter: 'blur(20px) saturate(180%)',
        zIndex: 1000,
        borderBottom: '1px solid rgba(37, 99, 235, 0.08)',
        height: '80px',
        boxShadow: '0 1px 8px rgba(0, 0, 0, 0.02)',
        isolation: 'isolate'
      }}>
        <div style={containerStyle}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            height: '80px',
            position: 'relative'
          }}>
            <a href="#" style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: '12px',
              textDecoration: 'none',
              cursor: 'pointer'
            }} onClick={(e) => {
              e.preventDefault();
              window.scrollTo({ top: 0, behavior: 'smooth' });
            }}>
              <div style={{
                width: '40px',
                height: '40px',
                background: 'linear-gradient(135deg, #ffffff 0%, #f3f4f6 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'hidden',
                boxShadow: '0 4px 20px rgba(37, 99, 235, 0.25)',
                transition: 'transform 0.2s ease'
              }} onMouseEnter={(e) => e.currentTarget.style.transform = 'scale(1.05)'}
                 onMouseLeave={(e) => e.currentTarget.style.transform = 'scale(1)'}>
                <img
                  src="/images/logo.jpg"
                  alt="PandaChinese Logo"
                  style={{
                    width: '85%',
                    height: '85%',
                    objectFit: 'cover',
                    borderRadius: '8px'
                  }}
                />
              </div>
              <span style={{
                fontSize: '24px',
                fontWeight: 'bold',
                color: '#2563eb'
              }}>
                {t('common:brand')}
              </span>
            </a>
            {/* Navigation - Center */}
            <nav style={{
              position: 'absolute',
              left: '50%',
              transform: 'translateX(-50%)',
              display: 'flex',
              alignItems: 'center',
              gap: '32px'
            }}>
              <a href="#ai-dialogue" style={{
                color: '#6b7280',
                textDecoration: 'none',
                fontWeight: '500',
                fontSize: '16px',
                whiteSpace: 'nowrap',
                transition: 'color 0.3s ease'
              }} className="nav-link">{t('navigation:nav.aiDialogue')}</a>
              <a href="#pinyin-learning" style={{
                color: '#6b7280',
                textDecoration: 'none',
                fontWeight: '500',
                fontSize: '16px',
                whiteSpace: 'nowrap',
                transition: 'color 0.3s ease'
              }} className="nav-link">{t('navigation:nav.pinyinLearning')}</a>
              <a href="#character-learning" style={{
                color: '#6b7280',
                textDecoration: 'none',
                fontWeight: '500',
                fontSize: '16px',
                whiteSpace: 'nowrap',
                transition: 'color 0.3s ease'
              }} className="nav-link">{t('navigation:nav.characterLearning')}</a>
              <a href="#idiom-learning" style={{
                color: '#6b7280',
                textDecoration: 'none',
                fontWeight: '500',
                fontSize: '16px',
                whiteSpace: 'nowrap',
                transition: 'color 0.3s ease'
              }} className="nav-link">{t('navigation:nav.idiomLearning')}</a>
              <a href="#listening-reading" style={{
                color: '#6b7280',
                textDecoration: 'none',
                fontWeight: '500',
                fontSize: '16px',
                whiteSpace: 'nowrap',
                transition: 'color 0.3s ease'
              }} className="nav-link">{t('navigation:nav.listeningReading')}</a>
              <a href="#leaderboard" style={{
                color: '#6b7280',
                textDecoration: 'none',
                fontWeight: '500',
                fontSize: '16px',
                whiteSpace: 'nowrap',
                transition: 'color 0.3s ease'
              }} className="nav-link">{t('navigation:nav.leaderboard')}</a>
            </nav>

            {/* Right side buttons */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '16px',
              marginLeft: 'auto'
            }}>
              <Dropdown menu={{ items: languageMenuItems }} placement="bottomRight">
                <Button
                  icon={<GlobalOutlined />}
                  type="text"
                  size="middle"
                  style={{
                    border: '1px solid rgba(0, 0, 0, 0.1)',
                    borderRadius: '8px',
                    fontSize: '14px'
                  }}
                >
                  {i18n.language === 'zh' ? '中文' : 'English'}
                </Button>
              </Dropdown>
              <Button
                type="primary"
                icon={<UserOutlined />}
                size="middle"
                style={{ fontSize: '14px' }}
              >
                {i18n.language === 'en' ? 'Login' : '登录'}
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main style={{ paddingTop: '80px' }}>
        {/* Hero Section */}
        <section id="hero" style={{
          paddingTop: '80px',
          paddingBottom: '0px',
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
          position: 'relative',
          overflow: 'hidden'
        }}>
          <div style={containerStyle}>
            <Row align="middle" gutter={[48, 32]}>
              <Col xs={24} lg={14}>
                <div style={{ textAlign: 'left', position: 'relative', zIndex: 2 }}>
                  <h1 style={{
                    fontSize: '56px',
                    fontWeight: 'bold',
                    marginBottom: '24px',
                    color: '#111827',
                    margin: '0 0 24px 0'
                  }} dangerouslySetInnerHTML={{ __html: t('pages:hero.title') }} />
                  <Paragraph style={{
                    fontSize: '20px',
                    color: '#6b7280',
                    maxWidth: '700px',
                    margin: '0 0 40px 0'
                  }}>
                    {t('pages:hero.subtitle')}
                  </Paragraph>
                  <Button
                    type="primary"
                    size="large"
                    icon={<PlayCircleOutlined />}
                    style={{
                      height: '56px',
                      padding: '0 32px',
                      fontSize: '18px'
                    }}
                  >
                    {t('pages:hero.cta')} <RightOutlined />
                  </Button>
                </div>
              </Col>
              <Col xs={24} lg={10}>
                <div style={{
                  position: 'relative',
                  height: '450px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'flex-start'
                }}>
                  <img
                    src="/images/panda.png"
                    alt="PandaChinese AI Learning"
                    style={{
                      
                      height: 'auto',
                      maxHeight: '450px',
                      objectFit: 'contain',
                      transform: 'translateX(-10%)'
                    }}
                  />
                </div>
              </Col>
            </Row>
          </div>
        </section>

        {/* Features Section - Three Step Learning Path */}
        <section id="features" style={{ ...sectionStyle, backgroundColor: '#ffffff' }}>
          <div style={containerStyle}>
            <div style={{ textAlign: 'center', marginBottom: '80px' }}>
              <Title level={2} style={{
                fontSize: '36px',
                fontWeight: 'bold',
                color: '#111827'
              }}>
                {t('pages:features.title')}
              </Title>
              <Paragraph style={{
                fontSize: '18px',
                color: '#6b7280',
                maxWidth: '600px',
                margin: '16px auto 0 auto'
              }}>
                {t('pages:features.subtitle')}
              </Paragraph>
            </div>

            <div style={{ position: 'relative' }}>
              {/* Connection Line */}
              <div style={{
                position: 'absolute',
                top: '120px',
                left: '50%',
                transform: 'translateX(-50%)',
                width: '80%',
                height: '2px',
                background: 'linear-gradient(90deg, #2563eb 0%, #3b82f6 50%, #60a5fa 100%)',
                zIndex: 1,
                display: 'none'
              }} className="hidden lg:block" />

              <Row gutter={[48, 48]} align="middle">
                {(t('pages:features.steps', { returnObjects: true }) as any[]).map((step: any, i: number) => (
                  <Col xs={24} lg={8} key={i}>
                    <div style={{
                      textAlign: 'center',
                      position: 'relative',
                      zIndex: 2
                    }}>
                      {/* Step Number Circle */}
                      <div style={{
                        width: '80px',
                        height: '80px',
                        background: `linear-gradient(135deg, ${i === 0 ? '#2563eb' : i === 1 ? '#3b82f6' : '#60a5fa'} 0%, ${i === 0 ? '#1d4ed8' : i === 1 ? '#2563eb' : '#3b82f6'} 100%)`,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        margin: '0 auto 24px auto',
                        boxShadow: '0 10px 30px rgba(37, 99, 235, 0.3)',
                        transform: 'translateY(0)',
                        transition: 'transform 0.3s ease'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}
                      onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
                      >
                        <span style={{
                          fontSize: '24px',
                          fontWeight: 'bold',
                          color: 'white'
                        }}>
                          {step.step}
                        </span>
                      </div>

                      {/* Step Content */}
                      <div style={{
                        backgroundColor: '#ffffff',
                        padding: '32px 24px',
                        borderRadius: '20px',
                        border: '1px solid #e5e7eb',
                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                        transition: 'transform 0.3s ease, box-shadow 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-5px)';
                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';
                      }}
                      >
                        <Title level={3} style={{
                          marginBottom: '8px',
                          color: '#111827',
                          fontSize: '24px'
                        }}>
                          {step.title}
                        </Title>
                        <Paragraph style={{
                          color: '#2563eb',
                          fontSize: '14px',
                          fontWeight: '600',
                          marginBottom: '16px',
                          textTransform: 'uppercase',
                          letterSpacing: '0.5px'
                        }}>
                          {step.subtitle}
                        </Paragraph>
                        <Paragraph style={{
                          color: '#6b7280',
                          marginBottom: '20px',
                          lineHeight: '1.6'
                        }}>
                          {step.desc}
                        </Paragraph>

                        {/* Feature Tags */}
                        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', justifyContent: 'center' }}>
                          {step.features.map((feature: string, fi: number) => (
                            <span key={fi} style={{
                              backgroundColor: '#f1f5f9',
                              color: '#475569',
                              padding: '4px 12px',
                              borderRadius: '20px',
                              fontSize: '12px',
                              fontWeight: '500'
                            }}>
                              {feature}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </Col>
                ))}
              </Row>
            </div>
          </div>
        </section>

        {/* Why Choose Us Section - Redesigned */}
        <section id="why" style={{
          ...sectionStyle,
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
          position: 'relative',
          overflow: 'hidden'
        }}>
          {/* Background Pattern */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: 'radial-gradient(circle at 25% 25%, rgba(37, 99, 235, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)',
            zIndex: 1
          }} />

          <div style={{ ...containerStyle, position: 'relative', zIndex: 2 }}>
            <div style={{ textAlign: 'center', marginBottom: '60px' }}>
              <Title level={2} style={{
                fontSize: '36px',
                fontWeight: 'bold',
                color: '#111827',
                marginBottom: '16px'
              }}>
                {t('pages:why.title')}
              </Title>
              <Paragraph style={{
                fontSize: '18px',
                color: '#6b7280',
                maxWidth: '700px',
                margin: '0 auto 40px auto'
              }}>
                {t('pages:why.subtitle')}
              </Paragraph>

              {/* Stats Row - Enlarged */}
              <Row gutter={[48, 24]} justify="center" style={{ marginBottom: '60px' }}>
                <Col xs={8} sm={8}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{
                      fontSize: '48px',
                      fontWeight: 'bold',
                      color: '#2563eb',
                      marginBottom: '8px',
                      textShadow: '0 2px 4px rgba(37, 99, 235, 0.2)'
                    }}>
                      {(t('pages:why.stats', { returnObjects: true }) as any).users}
                    </div>
                    <div style={{ fontSize: '16px', color: '#6b7280', fontWeight: '500' }}>
                      {i18n.language === 'zh' ? '全球用户' : 'Global Users'}
                    </div>
                  </div>
                </Col>
                <Col xs={8} sm={8}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{
                      fontSize: '48px',
                      fontWeight: 'bold',
                      color: '#2563eb',
                      marginBottom: '8px',
                      textShadow: '0 2px 4px rgba(37, 99, 235, 0.2)'
                    }}>
                      {(t('pages:why.stats', { returnObjects: true }) as any).satisfaction}
                    </div>
                    <div style={{ fontSize: '16px', color: '#6b7280', fontWeight: '500' }}>
                      {i18n.language === 'zh' ? '满意度' : 'Satisfaction'}
                    </div>
                  </div>
                </Col>
                <Col xs={8} sm={8}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{
                      fontSize: '48px',
                      fontWeight: 'bold',
                      color: '#2563eb',
                      marginBottom: '8px',
                      textShadow: '0 2px 4px rgba(37, 99, 235, 0.2)'
                    }}>
                      {(t('pages:why.stats', { returnObjects: true }) as any).improvement}
                    </div>
                    <div style={{ fontSize: '16px', color: '#6b7280', fontWeight: '500' }}>
                      {i18n.language === 'zh' ? '效率提升' : 'Efficiency Boost'}
                    </div>
                  </div>
                </Col>
              </Row>
            </div>

            {/* Highlights Grid */}
            <Row gutter={[32, 32]} style={{ marginBottom: '60px' }}>
              {(t('pages:why.highlights', { returnObjects: true }) as any[]).map((highlight: any, index: number) => (
                <Col xs={24} md={8} key={index}>
                  <div style={{
                    backgroundColor: '#ffffff',
                    padding: '40px 32px',
                    borderRadius: '24px',
                    textAlign: 'center',
                    boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1)',
                    border: '1px solid rgba(255, 255, 255, 0.8)',
                    backdropFilter: 'blur(10px)',
                    transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                    height: '100%'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-10px)';
                    e.currentTarget.style.boxShadow = '0 20px 60px rgba(0, 0, 0, 0.15)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 10px 40px rgba(0, 0, 0, 0.1)';
                  }}
                  >
                    <div style={{
                      fontSize: '48px',
                      marginBottom: '20px'
                    }}>
                      {highlight.icon}
                    </div>
                    <Title level={4} style={{
                      marginBottom: '12px',
                      color: '#111827'
                    }}>
                      {highlight.title}
                    </Title>
                    <Paragraph style={{
                      color: '#6b7280',
                      marginBottom: '16px',
                      fontSize: '16px'
                    }}>
                      {highlight.desc}
                    </Paragraph>
                    <Paragraph style={{
                      color: '#9ca3af',
                      fontSize: '14px',
                      margin: 0,
                      fontStyle: 'italic'
                    }}>
                      {highlight.detail}
                    </Paragraph>
                  </div>
                </Col>
              ))}
            </Row>

          </div>
        </section>

        {/* Reviews Section */}
        <section id="reviews" style={{ ...sectionStyle, backgroundColor: '#ffffff', paddingBottom: '40px' }}>
          <div style={containerStyle}>
            <div style={{ textAlign: 'center', marginBottom: '60px' }}>
              <Title level={2} style={{
                fontSize: '36px',
                fontWeight: 'bold',
                color: '#111827'
              }}>
                {t('pages:reviews.title')}
              </Title>
              <Paragraph style={{
                fontSize: '18px',
                color: '#6b7280',
                margin: '16px 0 0 0'
              }}>
                {t('pages:reviews.subtitle')}
              </Paragraph>
            </div>
            <div style={{
              overflow: 'hidden',
              position: 'relative',
              paddingTop: '20px',
              paddingBottom: '20px'
            }}>
              <div style={{
                position: 'absolute',
                left: 0,
                top: 0,
                bottom: 0,
                width: '100px',
                background: 'linear-gradient(to right, rgba(255,255,255,1), rgba(255,255,255,0))',
                zIndex: 2,
                pointerEvents: 'none'
              }} />
              <div style={{
                position: 'absolute',
                right: 0,
                top: 0,
                bottom: 0,
                width: '100px',
                background: 'linear-gradient(to left, rgba(255,255,255,1), rgba(255,255,255,0))',
                zIndex: 2,
                pointerEvents: 'none'
              }} />
              <div style={{
                display: 'flex',
                gap: '24px',
                animation: 'scrollReviews 40s linear infinite',
                width: 'fit-content'
              }}>
                {[...Array(3)].map((_, setIndex) => (
                  (t('pages:reviews.items', { returnObjects: true }) as any[]).map((review: any, index: number) => (
                    <div key={`${setIndex}-${index}`} style={{
                      minWidth: '340px',
                      padding: '32px',
                      backgroundColor: '#ffffff',
                      borderRadius: '20px',
                      border: '1px solid #e5e7eb',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      transition: 'transform 0.3s ease, box-shadow 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-5px)';
                      e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';
                    }}
                    >
                      <div style={{ marginBottom: '20px', textAlign: 'center' }}>
                        {[...Array(5)].map((_, i) => (
                          <span key={i} style={{
                            color: '#fbbf24',
                            fontSize: '20px',
                            marginRight: '2px',
                            textShadow: '0 1px 2px rgba(251, 191, 36, 0.3)'
                          }}>★</span>
                        ))}
                      </div>
                      <Paragraph style={{
                        color: '#374151',
                        marginBottom: '20px',
                        fontSize: '16px',
                        lineHeight: '1.7',
                        fontStyle: 'italic',
                        textAlign: 'center'
                      }}>
                        "{review.review}"
                      </Paragraph>
                      <div style={{
                        fontWeight: '600',
                        color: '#2563eb',
                        fontSize: '15px',
                        textAlign: 'center',
                        borderTop: '1px solid #f1f5f9',
                        paddingTop: '16px'
                      }}>
                        {review.name}
                      </div>
                    </div>
                  ))
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section id="cta" style={{ ...sectionStyle, backgroundColor: '#2563eb', paddingTop: '40px' }}>
          <div style={containerStyle}>
            <div style={{ textAlign: 'center' }}>
              <Title level={2} style={{
                fontSize: '36px',
                fontWeight: 'bold',
                color: '#ffffff',
                marginBottom: '16px'
              }}>
                {t('pages:cta.title')}
              </Title>
              <Paragraph style={{
                fontSize: '18px',
                color: 'rgba(255, 255, 255, 0.9)',
                maxWidth: '600px',
                margin: '0 auto 40px auto'
              }}>
                {t('pages:cta.subtitle')}
              </Paragraph>
              <Button
                type="default"
                size="large"
                style={{
                  height: '56px',
                  padding: '0 32px',
                  fontSize: '18px',
                  backgroundColor: '#ffffff',
                  borderColor: '#ffffff',
                  color: '#2563eb'
                }}
              >
                {t('pages:cta.button')}
              </Button>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer style={{ backgroundColor: '#111827', color: '#ffffff', padding: '60px 0 30px 0' }}>
        <div style={containerStyle}>
          <Row gutter={[32, 32]}>
            <Col xs={24} sm={24} md={8} lg={8}>
              <div style={{ marginBottom: '24px' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
                  <div style={{
                    width: '40px',
                    height: '40px',
                    background: 'linear-gradient(135deg, #ffffff 0%, #f3f4f6 100%)',
                    borderRadius: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    overflow: 'hidden'
                  }}>
                    <img
                      src="/images/logo.jpg"
                      alt="PandaChinese Logo"
                      style={{
                        width: '85%',
                        height: '85%',
                        objectFit: 'cover',
                        borderRadius: '8px'
                      }}
                    />
                  </div>
                  <span style={{ fontSize: '24px', fontWeight: 'bold', color: '#ffffff' }}>
                    {t('common:brand')}
                  </span>
                </div>
                <Paragraph style={{ color: '#9ca3af', marginBottom: '24px' }}>
                  {t('pages:footer.desc')}
                </Paragraph>
                <Button
                  type="primary"
                  size="large"
                  style={{ backgroundColor: '#2563eb', borderColor: '#2563eb' }}
                >
                  {t('pages:footer.button')}
                </Button>
              </div>
            </Col>
            <Col xs={12} sm={12} md={4} lg={4}>
              <Title level={5} style={{ color: '#ffffff', marginBottom: '16px' }}>
                {t('pages:footer.navigation.title')}
              </Title>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                {(t('pages:footer.navigation.links', { returnObjects: true }) as any[]).map((link: any) => (
                  <a key={link.name} href={link.href} style={{
                    color: '#9ca3af',
                    textDecoration: 'none',
                    fontSize: '14px'
                  }} className="footer-link">
                    {link.name}
                  </a>
                ))}
              </div>
            </Col>
            <Col xs={12} sm={12} md={4} lg={4}>
              <Title level={5} style={{ color: '#ffffff', marginBottom: '16px' }}>
                {t('pages:footer.about.title')}
              </Title>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                {(t('pages:footer.about.links', { returnObjects: true }) as any[]).map((link: any) => (
                  <a key={link.name} href={link.href} style={{
                    color: '#9ca3af',
                    textDecoration: 'none',
                    fontSize: '14px'
                  }} className="footer-link">
                    {link.name}
                  </a>
                ))}
              </div>
            </Col>
            <Col xs={24} sm={24} md={8} lg={8}>
              <Title level={5} style={{ color: '#ffffff', marginBottom: '16px' }}>
                {t('pages:footer.social.title')}
              </Title>
              <div style={{
                display: 'flex',
                gap: '16px',
                marginBottom: '24px',
                flexWrap: 'wrap'
              }}>
                <a href="#" style={{
                  color: '#9ca3af',
                  fontSize: '20px',
                  transition: 'color 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '40px',
                  height: '40px',
                  borderRadius: '8px',
                  backgroundColor: 'rgba(156, 163, 175, 0.1)'
                }}
                onMouseEnter={(e) => e.currentTarget.style.color = '#07c160'}
                onMouseLeave={(e) => e.currentTarget.style.color = '#9ca3af'}
                title="WeChat"
                >
                  <WechatOutlined />
                </a>
                <a href="#" style={{
                  color: '#9ca3af',
                  fontSize: '20px',
                  transition: 'color 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '40px',
                  height: '40px',
                  borderRadius: '8px',
                  backgroundColor: 'rgba(156, 163, 175, 0.1)'
                }}
                onMouseEnter={(e) => e.currentTarget.style.color = '#000000'}
                onMouseLeave={(e) => e.currentTarget.style.color = '#9ca3af'}
                title="TikTok"
                >
                  <TikTokOutlined />
                </a>
                <a href="#" style={{
                  color: '#9ca3af',
                  fontSize: '18px',
                  transition: 'color 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '40px',
                  height: '40px',
                  borderRadius: '8px',
                  backgroundColor: 'rgba(156, 163, 175, 0.1)',
                  fontWeight: 'bold'
                }}
                onMouseEnter={(e) => e.currentTarget.style.color = '#0088cc'}
                onMouseLeave={(e) => e.currentTarget.style.color = '#9ca3af'}
                title="Telegram"
                >
                  T
                </a>
                <a href="#" style={{
                  color: '#9ca3af',
                  fontSize: '18px',
                  transition: 'color 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '40px',
                  height: '40px',
                  borderRadius: '8px',
                  backgroundColor: 'rgba(156, 163, 175, 0.1)',
                  fontWeight: 'bold'
                }}
                onMouseEnter={(e) => e.currentTarget.style.color = '#5865f2'}
                onMouseLeave={(e) => e.currentTarget.style.color = '#9ca3af'}
                title="Discord"
                >
                  D
                </a>
                <a href="#" style={{
                  color: '#9ca3af',
                  fontSize: '20px',
                  transition: 'color 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '40px',
                  height: '40px',
                  borderRadius: '8px',
                  backgroundColor: 'rgba(156, 163, 175, 0.1)'
                }}
                onMouseEnter={(e) => e.currentTarget.style.color = '#e4405f'}
                onMouseLeave={(e) => e.currentTarget.style.color = '#9ca3af'}
                title="Instagram"
                >
                  <InstagramOutlined />
                </a>
                <a href="#" style={{
                  color: '#9ca3af',
                  fontSize: '20px',
                  transition: 'color 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '40px',
                  height: '40px',
                  borderRadius: '8px',
                  backgroundColor: 'rgba(156, 163, 175, 0.1)'
                }}
                onMouseEnter={(e) => e.currentTarget.style.color = '#1877f2'}
                onMouseLeave={(e) => e.currentTarget.style.color = '#9ca3af'}
                title="Facebook"
                >
                  <FacebookOutlined />
                </a>
              </div>
              <div style={{ marginBottom: '16px' }}>
                <Title level={5} style={{ color: '#ffffff', marginBottom: '8px', fontSize: '14px' }}>
                  {t('common:language.title')}
                </Title>
                <Dropdown menu={{ items: languageMenuItems }} placement="topRight">
                  <Button
                    type="text"
                    size="middle"
                    style={{
                      color: '#9ca3af',
                      border: '1px solid rgba(156, 163, 175, 0.3)',
                      borderRadius: '8px',
                      padding: '4px 12px',
                      height: 'auto',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}
                  >
                    <GlobalOutlined style={{ fontSize: '14px' }} />
                    <span style={{ fontSize: '14px' }}>
                      {i18n.language === 'zh' ? '中文' : 'English'}
                    </span>
                  </Button>
                </Dropdown>
              </div>
            </Col>
          </Row>
          <div style={{
            borderTop: '1px solid #374151',
            paddingTop: '24px',
            marginTop: '40px',
            textAlign: 'center'
          }}>
            <Paragraph style={{ color: '#9ca3af', margin: 0, fontSize: '14px' }}>
              {t('pages:footer.copyright', { year: new Date().getFullYear() })}
            </Paragraph>
          </div>
        </div>
      </footer>

      <style>{`
        html {
          overflow-y: scroll;
        }
        @keyframes scrollReviews {
          0% { transform: translateX(0); }
          100% { transform: translateX(-33.333%); }
        }
        .footer-link:hover {
          color: #2563eb !important;
        }
        .nav-link:hover {
          color: #2563eb !important;
        }
        @media (max-width: 768px) {
          nav {
            display: none !important;
          }
        }
      `}</style>
    </div>
  )
}

export default App
