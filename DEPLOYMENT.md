# PandaChinese 部署指南

## Cloudflare Pages 部署

### 自动部署配置

项目已配置为在 Cloudflare Pages 上自动部署。以下是关键配置：

#### 构建设置
- **构建命令**: `cd frontend && npm ci && npm run build`
- **构建输出目录**: `frontend/dist`
- **Node.js 版本**: 20.18.0 (通过 `.node-version` 文件指定)

#### 环境变量
在 Cloudflare Pages 控制台中设置以下环境变量：

```
VITE_SUPABASE_URL=你的Supabase项目URL
VITE_SUPABASE_ANON_KEY=你的Supabase匿名密钥
```

#### 项目结构
```
pandachinese/
├── frontend/                    # React 前端应用
│   ├── dist/                   # 构建输出目录 (自动生成)
│   ├── public/                 # 静态资源
│   │   ├── _redirects         # SPA 路由重定向规则
│   │   └── _headers           # HTTP 头部配置
│   ├── src/                   # 源代码
│   └── package.json           # 前端依赖配置
├── wrangler.toml              # Cloudflare 配置
├── .node-version              # Node.js 版本指定
└── build.sh                   # 构建脚本
```

### 手动部署步骤

1. **克隆仓库**
   ```bash
   git clone https://github.com/songweibiao/pandachinese.git
   cd pandachinese
   ```

2. **安装依赖**
   ```bash
   cd frontend
   npm ci
   ```

3. **构建项目**
   ```bash
   npm run build
   ```

4. **部署到 Cloudflare Pages**
   - 登录 Cloudflare Pages 控制台
   - 连接 GitHub 仓库
   - 设置构建配置：
     - 构建命令: `cd frontend && npm ci && npm run build`
     - 构建输出目录: `frontend/dist`
   - 配置环境变量
   - 触发部署

### 故障排除

#### 常见问题

1. **TypeScript 编译错误**
   - 确保使用 Node.js 20+ 版本
   - 检查 TypeScript 配置是否正确

2. **依赖安装失败**
   - 清理 node_modules: `rm -rf frontend/node_modules`
   - 重新安装: `cd frontend && npm ci`

3. **构建失败**
   - 检查环境变量是否正确设置
   - 确认所有依赖都已正确安装

#### 调试构建

本地测试构建：
```bash
# 使用项目构建脚本
./build.sh

# 或手动构建
cd frontend
npm ci
npm run build
```

### 性能优化

项目包含以下优化配置：

1. **缓存策略** (`_headers` 文件)
   - 静态资源长期缓存
   - HTML 文件短期缓存
   - 安全头部配置

2. **SPA 路由** (`_redirects` 文件)
   - 所有路由重定向到 index.html
   - 支持客户端路由

3. **构建优化**
   - Vite 构建优化
   - 代码分割和懒加载
   - 资源压缩和优化

### 监控和维护

- 使用 Cloudflare Analytics 监控网站性能
- 定期更新依赖包
- 监控构建日志以发现潜在问题
