# PandaChinese

AI驱动的中文学习平台，专注于拼音、听、说、读全方位训练。

## 项目特性

- 🤖 **AI对话**: 智能角色扮演对话练习，沉浸式中文交流体验
- 🎵 **拼音学习**: 完整的声母、韵母、声调训练，AI发音纠正
- 📝 **汉字学习**: 汉字笔画、结构、词汇扩展训练
- 🏮 **成语学习**: 成语故事、用法、文化背景深度学习
- 🎧 **听读中心**: 分级阅读材料和听力训练
- 🏆 **排行榜**: 学习激励和成就系统，与全球学习者竞技

## 技术栈

### 前端
- **框架**: React + Vite
- **样式**: TailwindCSS + 现代化蓝色系UI设计
- **构建工具**: Vite

### 后端
- **BaaS**: Supabase (PostgreSQL + 认证 + 存储)
- **AI服务**: Groq/OpenAI API
- **语音识别**: Web Speech API
- **语音合成**: Web Speech API/ElevenLabs

## 开发环境

### 系统要求
- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

### 代码检查
```bash
npm run lint
npm run typecheck
```

## 项目结构

```
pandachinese/
├── frontend/          # 前端React应用
│   ├── src/          # 源代码
│   ├── public/       # 静态资源
│   └── ...           # 配置文件
├── docs/            # 文档
├── scripts/         # 脚本工具
└── README.md        # 项目说明
```

## 贡献指南

1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

项目维护者: Kilo Code
项目链接: [https://github.com/yourusername/pandachinese](https://github.com/yourusername/pandachinese)