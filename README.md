# PandaChinese

AI驱动的中文学习平台，专注于拼音、听、说、读全方位训练。

## 项目特性

- 🤖 **AI对话**: 智能角色扮演对话练习，沉浸式中文交流体验
- 🎵 **拼音学习**: 完整的声母、韵母、声调训练，AI发音纠正
- 📝 **汉字学习**: 汉字笔画、结构、词汇扩展训练
- 🏮 **成语学习**: 成语故事、用法、文化背景深度学习
- 🎧 **听读中心**: 分级阅读材料和听力训练
- 🏆 **排行榜**: 学习激励和成就系统，与全球学习者竞技

## 技术栈

### 前端
- **框架**: React + Vite
- **样式**: TailwindCSS + 现代化蓝色系UI设计
- **构建工具**: Vite

### 后端
- **BaaS**: Supabase (PostgreSQL + 认证 + 存储)
- **AI服务**: Groq/OpenAI API
- **语音识别**: Web Speech API
- **语音合成**: Web Speech API/ElevenLabs

## 开发环境

### 系统要求
- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

### 代码检查
```bash
npm run lint
npm run typecheck
```

## 项目结构

```
pandachinese/
├── frontend/                    # 前端React应用
│   ├── src/                    # 源代码目录
│   │   ├── components/         # React组件
│   │   │   ├── Error.tsx      # 错误处理组件
│   │   │   └── Loading.tsx    # 加载状态组件
│   │   ├── pages/             # 页面组件
│   │   ├── hooks/             # 自定义React Hooks
│   │   ├── stores/            # 状态管理
│   │   ├── services/          # API服务层
│   │   │   └── supabase.ts    # Supabase客户端配置
│   │   ├── types/             # TypeScript类型定义
│   │   │   └── index.ts       # 全局类型定义
│   │   ├── utils/             # 工具函数
│   │   │   └── index.ts       # 通用工具函数
│   │   ├── assets/            # 静态资源
│   │   │   ├── images/        # 图片资源
│   │   │   └── sounds/        # 音频资源
│   │   ├── i18n/              # 国际化配置
│   │   │   ├── index.ts       # i18n主配置
│   │   │   └── locales/       # 语言包
│   │   │       ├── zh/        # 中文语言包
│   │   │       │   ├── index.ts
│   │   │       │   ├── common.json
│   │   │       │   ├── navigation.json
│   │   │       │   └── pages.json
│   │   │       └── en/        # 英文语言包
│   │   │           ├── index.ts
│   │   │           ├── common.json
│   │   │           ├── navigation.json
│   │   │           └── pages.json
│   │   ├── App.tsx            # 根组件
│   │   ├── main.tsx           # 应用入口
│   │   └── index.css          # 全局样式
│   ├── public/                # 静态资源目录
│   │   └── images/            # 公共图片资源
│   │       ├── logo.jpg       # 项目Logo
│   │       ├── panda.gif      # 熊猫动画
│   │       ├── panda.ico      # 网站图标
│   │       └── panda.png      # 熊猫图标
│   ├── images/                # 额外图片资源
│   │   ├── 1panda-.png        # 熊猫图片
│   │   └── panda.gif          # 熊猫动画
│   ├── package.json           # 前端依赖配置
│   ├── package-lock.json      # 依赖锁定文件
│   ├── vite.config.ts         # Vite构建配置
│   ├── tsconfig.json          # TypeScript配置
│   ├── tsconfig.node.json     # Node.js TypeScript配置
│   ├── tailwind.config.js     # TailwindCSS配置
│   ├── postcss.config.js      # PostCSS配置
│   └── index.html             # HTML模板
├── docs/                      # 项目文档
├── .augment/                  # Augment配置目录
│   └── rules/                 # 代码规则配置
├── package.json               # 根项目配置
├── .gitignore                 # Git忽略文件
├── README.md                  # 项目说明文档
├── PandaChinese_PRD.md        # 产品需求文档
└── CLAUDE.md                  # Claude AI助手配置
```

## 贡献指南

1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

项目维护者: Kilo Code
项目链接: [https://github.com/yourusername/pandachinese](https://github.com/yourusername/pandachinese)